#!/usr/bin/env python3
"""
模拟FRPS服务器
用于测试FRP管理系统功能
"""

import sys
import time
import signal
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import threading

class MockFRPSHandler(BaseHTTPRequestHandler):
    """模拟FRPS的HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/api/status':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            
            status = {
                "version": "0.52.3",
                "status": "running",
                "bind_port": 7000,
                "clients": 0,
                "proxies": 0
            }
            self.wfile.write(json.dumps(status).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """处理POST请求（认证插件会发送到这里）"""
        # 这里可以处理来自认证插件的请求
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {"status": "ok"}
        self.wfile.write(json.dumps(response).encode())
    
    def log_message(self, format, *args):
        """重写日志方法，输出到标准输出"""
        print(f"[MOCK-FRPS] {format % args}")

class MockFRPS:
    def __init__(self, config_file=None):
        self.config_file = config_file
        self.running = False
        self.server = None
        self.bind_port = 7000
        
    def parse_config(self):
        """解析配置文件"""
        if self.config_file:
            try:
                # 这里可以解析TOML配置文件
                print(f"[MOCK-FRPS] 加载配置文件: {self.config_file}")
                # 简单模拟，实际应该解析TOML
                self.bind_port = 7000
            except Exception as e:
                print(f"[MOCK-FRPS] 配置文件解析失败: {e}")
                return False
        return True
    
    def start_server(self):
        """启动模拟服务器"""
        try:
            # 启动HTTP服务器用于状态查询
            self.server = HTTPServer(('127.0.0.1', 8080), MockFRPSHandler)
            server_thread = threading.Thread(target=self.server.serve_forever)
            server_thread.daemon = True
            server_thread.start()

            print(f"[MOCK-FRPS] HTTP状态服务器启动在端口 8080")

            # 模拟监听主端口
            print(f"[MOCK-FRPS] 模拟监听端口 {self.bind_port}")

            return True
        except Exception as e:
            print(f"[MOCK-FRPS] 服务器启动失败: {e}")
            return False
    
    def run(self):
        """运行模拟FRPS"""
        print("[MOCK-FRPS] 启动模拟FRP服务器...")
        
        if not self.parse_config():
            sys.exit(1)
        
        if not self.start_server():
            sys.exit(1)
        
        self.running = True
        print(f"[MOCK-FRPS] 服务器运行中，监听端口 {self.bind_port}")
        print("[MOCK-FRPS] 按 Ctrl+C 停止服务器")
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 主循环
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        self.stop()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n[MOCK-FRPS] 收到信号 {signum}，正在停止...")
        self.running = False
    
    def stop(self):
        """停止服务器"""
        print("[MOCK-FRPS] 正在停止服务器...")
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        print("[MOCK-FRPS] 服务器已停止")

def main():
    """主函数"""
    config_file = None
    
    # 解析命令行参数
    if len(sys.argv) >= 3 and sys.argv[1] == '-c':
        config_file = sys.argv[2]
    
    # 创建并运行模拟FRPS
    mock_frps = MockFRPS(config_file)
    mock_frps.run()

if __name__ == '__main__':
    main()
