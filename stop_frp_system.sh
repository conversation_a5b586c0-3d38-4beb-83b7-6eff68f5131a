#!/bin/bash

# FRP系统停止脚本
# 停止所有相关服务并清理日志

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGIN_DIR="$SCRIPT_DIR/frp_auth_plugin"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 停止进程
stop_process() {
    local process_name="$1"
    local pattern="$2"
    local pid_file="$3"
    
    log "停止 $process_name..."
    
    # 通过PID文件停止
    if [ -n "$pid_file" ] && [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            kill -TERM "$pid" 2>/dev/null
            sleep 2
            
            # 检查是否已停止
            if ps -p "$pid" > /dev/null 2>&1; then
                warn "强制停止 $process_name (PID: $pid)"
                kill -KILL "$pid" 2>/dev/null
            fi
        fi
        rm -f "$pid_file"
    fi
    
    # 通过进程名停止
    local pids=$(pgrep -f "$pattern" 2>/dev/null)
    if [ -n "$pids" ]; then
        for pid in $pids; do
            if ps -p "$pid" > /dev/null 2>&1; then
                kill -TERM "$pid" 2>/dev/null
                sleep 1
                
                # 如果还在运行，强制停止
                if ps -p "$pid" > /dev/null 2>&1; then
                    kill -KILL "$pid" 2>/dev/null
                fi
            fi
        done
    fi
    
    # 验证是否已停止
    if pgrep -f "$pattern" > /dev/null 2>&1; then
        error "$process_name 停止失败"
        return 1
    else
        log "$process_name 已停止"
        return 0
    fi
}

# 清理日志文件
cleanup_logs() {
    log "清理日志文件..."
    
    if [ -d "$PLUGIN_DIR" ]; then
        cd "$PLUGIN_DIR"
        
        # 使用Python脚本清理日志
        if [ -f "log_manager.py" ]; then
            python3 -c "from log_manager import cleanup_logs; cleanup_logs()" 2>/dev/null || true
        fi
        
        # 手动清理其他日志文件
        rm -f *.log 2>/dev/null || true
        rm -f log/*.log 2>/dev/null || true
        
        cd "$SCRIPT_DIR"
    fi
    
    # 清理主目录的日志
    rm -f "$SCRIPT_DIR"/*.log 2>/dev/null || true
    rm -f "$SCRIPT_DIR/log"/*.log 2>/dev/null || true
    
    log "日志文件已清理"
}

# 清理PID文件
cleanup_pid_files() {
    log "清理PID文件..."
    
    rm -f "$PLUGIN_DIR"/*.pid 2>/dev/null || true
    rm -f "$SCRIPT_DIR"/*.pid 2>/dev/null || true
    
    log "PID文件已清理"
}

# 显示停止前状态
show_before_status() {
    echo -e "${BLUE}"
    echo "=================================================================="
    echo "                    停止 FRP 综合管理系统"
    echo "=================================================================="
    echo -e "${NC}"
    
    info "当前运行的服务:"
    
    # FRPS状态
    if pgrep -f "frps.*frps.toml" > /dev/null; then
        echo -e "  FRPS服务:     ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  FRPS服务:     ${RED}❌ 未运行${NC}"
    fi
    
    # 认证API状态
    if pgrep -f "auth_api.py" > /dev/null; then
        echo -e "  认证API:      ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  认证API:      ${RED}❌ 未运行${NC}"
    fi
    
    # Web界面状态
    if pgrep -f "web_ui.py" > /dev/null; then
        echo -e "  Web管理界面:  ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  Web管理界面:  ${RED}❌ 未运行${NC}"
    fi
    
    echo ""
}

# 显示停止后状态
show_after_status() {
    echo ""
    log "停止后状态检查:"
    
    local all_stopped=true
    
    # FRPS状态
    if pgrep -f "frps.*frps.toml" > /dev/null; then
        echo -e "  FRPS服务:     ${RED}❌ 仍在运行${NC}"
        all_stopped=false
    else
        echo -e "  FRPS服务:     ${GREEN}✅ 已停止${NC}"
    fi
    
    # 认证API状态
    if pgrep -f "auth_api.py" > /dev/null; then
        echo -e "  认证API:      ${RED}❌ 仍在运行${NC}"
        all_stopped=false
    else
        echo -e "  认证API:      ${GREEN}✅ 已停止${NC}"
    fi
    
    # Web界面状态
    if pgrep -f "web_ui.py" > /dev/null; then
        echo -e "  Web管理界面:  ${RED}❌ 仍在运行${NC}"
        all_stopped=false
    else
        echo -e "  Web管理界面:  ${GREEN}✅ 已停止${NC}"
    fi
    
    echo ""
    
    if $all_stopped; then
        log "所有服务已成功停止"
    else
        error "部分服务停止失败，请手动检查"
    fi
}

# 主停止流程
main() {
    show_before_status
    
    # 1. 停止Web界面
    stop_process "Web管理界面" "web_ui.py" "$PLUGIN_DIR/web_ui.pid"
    
    # 2. 停止认证API
    stop_process "认证API" "auth_api.py" "$PLUGIN_DIR/auth_api.pid"
    
    # 3. 停止FRPS服务
    stop_process "FRPS服务" "frps.*frps.toml\|frps_mock.py" "$PLUGIN_DIR/frps.pid"
    
    # 4. 清理PID文件
    cleanup_pid_files
    
    # 5. 清理日志文件
    cleanup_logs
    
    # 6. 显示最终状态
    show_after_status
    
    log "FRP系统停止完成!"
    echo ""
    echo "💡 提示:"
    echo "  - 使用 './start_frp_system.sh' 重新启动系统"
    echo "  - 所有日志文件已清理"
}

# 执行主函数
main "$@"
