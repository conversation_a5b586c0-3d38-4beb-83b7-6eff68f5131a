#!/bin/bash

# FRP服务器控制脚本
# 用于启动、停止、重启FRPS服务

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRPS_DIR="$(dirname "$SCRIPT_DIR")"
FRPS_BIN="$FRPS_DIR/frps"
FRPS_CONFIG="$FRPS_DIR/frps.toml"
PID_FILE="$SCRIPT_DIR/frps.pid"
LOG_FILE="$SCRIPT_DIR/log/frps.log"

# 确保日志目录存在
mkdir -p "$SCRIPT_DIR/log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 检查FRPS是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 获取FRPS进程ID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 启动FRPS
start_frps() {
    if is_running; then
        warn "FRPS is already running (PID: $(get_pid))"
        return 1
    fi

    if [ ! -f "$FRPS_BIN" ]; then
        error "FRPS binary not found: $FRPS_BIN"
        return 1
    fi

    if [ ! -f "$FRPS_CONFIG" ]; then
        error "FRPS config not found: $FRPS_CONFIG"
        return 1
    fi

    log "Starting FRPS..."
    
    # 启动FRPS服务
    nohup "$FRPS_BIN" -c "$FRPS_CONFIG" >> "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待一下确认启动成功
    sleep 2
    
    if is_running; then
        log "FRPS started successfully (PID: $pid)"
        
        # 记录启动到数据文件
        python3 -c "
import sys
sys.path.append('$SCRIPT_DIR')
from data_handler import DataHandler
dh = DataHandler()
dh.log_frps_start()
" 2>/dev/null || true
        
        return 0
    else
        error "Failed to start FRPS"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止FRPS
stop_frps() {
    if ! is_running; then
        warn "FRPS is not running"
        return 1
    fi

    local pid=$(get_pid)
    log "Stopping FRPS (PID: $pid)..."
    
    # 发送TERM信号
    kill -TERM "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            break
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 如果还没结束，强制杀死
    if ps -p "$pid" > /dev/null 2>&1; then
        warn "Force killing FRPS..."
        kill -KILL "$pid" 2>/dev/null
        sleep 1
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    if ! ps -p "$pid" > /dev/null 2>&1; then
        log "FRPS stopped successfully"
        
        # 记录停止到数据文件
        python3 -c "
import sys
sys.path.append('$SCRIPT_DIR')
from data_handler import DataHandler
dh = DataHandler()
dh.log_frps_stop()
" 2>/dev/null || true
        
        return 0
    else
        error "Failed to stop FRPS"
        return 1
    fi
}

# 重启FRPS
restart_frps() {
    log "Restarting FRPS..."
    stop_frps
    sleep 2
    start_frps
}

# 显示状态
status_frps() {
    if is_running; then
        local pid=$(get_pid)
        log "FRPS is running (PID: $pid)"
        
        # 显示进程信息
        echo "Process info:"
        ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        
        # 显示端口监听情况
        echo ""
        echo "Listening ports:"
        netstat -tlnp 2>/dev/null | grep "$pid" || true
        
        return 0
    else
        warn "FRPS is not running"
        return 1
    fi
}

# 显示日志
logs_frps() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        error "Log file not found: $LOG_FILE"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-}" in
        start)
            start_frps
            ;;
        stop)
            stop_frps
            ;;
        restart)
            restart_frps
            ;;
        status)
            status_frps
            ;;
        logs)
            logs_frps
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|logs}"
            echo ""
            echo "Commands:"
            echo "  start   - Start FRPS service"
            echo "  stop    - Stop FRPS service"
            echo "  restart - Restart FRPS service"
            echo "  status  - Show FRPS status"
            echo "  logs    - Show FRPS logs (tail -f)"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
