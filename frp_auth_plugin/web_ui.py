from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from data_handler import DataHandler
import subprocess
import os
import logging
from log_manager import get_logger

logger = get_logger('WebUI', 'web_ui.log')

app = Flask(__name__)
app.secret_key = 'frp_management_secret_key_change_in_production'
data_handler = DataHandler()

# 添加时间格式化过滤器
import datetime

@app.template_filter('timestamp_to_date')
def timestamp_to_date(timestamp):
    """将时间戳转换为可读的日期格式"""
    try:
        dt = datetime.datetime.fromtimestamp(float(timestamp))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except (ValueError, TypeError):
        return '-'

@app.route('/')
def index():
    return render_template('index.html',
                         frps_status=get_frps_status(),
                         ports=data_handler.get_port_allocations(),
                         users=data_handler.get_all_users(),
                         system_stats=get_system_stats())

@app.route('/users')
def user_list():
    """用户管理页面"""
    users = data_handler.get_all_users()
    return render_template('users.html', users=users)

@app.route('/user/add', methods=['GET', 'POST'])
def add_user():
    """添加用户"""
    if request.method == 'POST':
        username = request.form.get('username')
        token = request.form.get('token')
        allow_ports = request.form.get('allow_ports')
        max_proxies = int(request.form.get('max_proxies', 10))

        if data_handler.add_user(username, token, allow_ports, max_proxies):
            flash(f'用户 {username} 添加成功', 'success')
            return redirect(url_for('user_list'))
        else:
            flash('添加用户失败，用户名可能已存在或端口范围格式错误', 'error')

    return render_template('add_user.html')

@app.route('/user/edit/<username>', methods=['GET', 'POST'])
def edit_user(username):
    """编辑用户"""
    user = data_handler.get_user(username)
    if not user:
        flash('用户不存在', 'error')
        return redirect(url_for('user_list'))

    if request.method == 'POST':
        updates = {}
        if request.form.get('token'):
            updates['token'] = request.form.get('token')
        if request.form.get('allow_ports'):
            updates['allow_ports'] = request.form.get('allow_ports')
        if request.form.get('max_proxies'):
            updates['max_proxies'] = int(request.form.get('max_proxies'))
        if request.form.get('status'):
            updates['status'] = request.form.get('status')

        if data_handler.update_user(username, **updates):
            flash(f'用户 {username} 更新成功', 'success')
            return redirect(url_for('user_list'))
        else:
            flash('更新用户失败', 'error')

    return render_template('edit_user.html', user=user)

@app.route('/user/delete/<username>')
def delete_user(username):
    """删除用户"""
    if data_handler.delete_user(username):
        flash(f'用户 {username} 删除成功', 'success')
    else:
        flash('删除用户失败', 'error')
    return redirect(url_for('user_list'))

@app.route('/ports')
def port_list():
    """端口管理页面"""
    ports = data_handler.get_port_allocations()
    return render_template('ports.html', ports=ports)

@app.route('/port/deallocate/<int:port>')
def deallocate_port(port):
    """释放端口"""
    if data_handler.deallocate_port(port):
        flash(f'端口 {port} 释放成功', 'success')
    else:
        flash('释放端口失败', 'error')
    return redirect(url_for('port_list'))

@app.route('/api/frps/<action>')
def control_frps(action):
    try:
        script_path = os.path.join(os.path.dirname(__file__), 'frp_control.sh')
        result = subprocess.run(
            [script_path, action],
            capture_output=True, text=True
        )
        return jsonify({
            "status": "success",
            "output": result.stdout,
            "error": result.stderr,
            "return_code": result.returncode
        })
    except Exception as e:
        logger.error(f"FRPS control error: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/status')
def status():
    return jsonify({
        "frps": get_frps_status(),
        "system": get_system_stats(),
        "statistics": data_handler.get_statistics()
    })

@app.route('/api/users')
def api_users():
    """API: 获取用户列表"""
    return jsonify(data_handler.get_all_users())

@app.route('/api/ports')
def api_ports():
    """API: 获取端口分配"""
    return jsonify(data_handler.get_port_allocations())

def get_frps_status():
    try:
        # 使用pgrep检查frps进程
        result = subprocess.run(['pgrep', '-f', 'frps.*frps.toml'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            pid = result.stdout.strip().split('\n')[0]
            return {
                "running": True,
                "pid": int(pid),
                "cmd": "frps -c frps.toml"
            }
    except Exception:
        pass
    return {"running": False}

def get_system_stats():
    return {
        "load": [0.0, 0.0, 0.0],  # 简化的负载信息
        "memory": {"percent": 0.0, "total": 0, "available": 0},  # 简化的内存信息
        "disks": {"percent": 0.0, "total": 0, "free": 0}  # 简化的磁盘信息
    }

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7007)