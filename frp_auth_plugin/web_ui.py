from flask import Flask, render_template, request, jsonify
from data_handler import DataHandler
import subprocess
import psutil
import os

app = Flask(__name__)
data_handler = DataHandler()

@app.route('/')
def index():
    return render_template('index.html',
                         frps_status=get_frps_status(),
                         ports=data_handler.get_port_allocations(),
                         users=data_handler.get_all_users(),
                         system_stats=get_system_stats())

@app.route('/api/frps/<action>')
def control_frps(action):
    try:
        result = subprocess.run(
            ['/frp/frp_control.sh', action],
            capture_output=True, text=True
        )
        return jsonify({
            "status": "success",
            "output": result.stdout,
            "error": result.stderr
        })
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/status')
def status():
    return jsonify({
        "frps": get_frps_status(),
        "system": get_system_stats()
    })

def get_frps_status():
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        if 'frps' in ' '.join(proc.info['cmdline'] or []):
            return {
                "running": True,
                "pid": proc.info['pid'],
                "cmd": ' '.join(proc.info['cmdline'] or [])
            }
    return {"running": False}

def get_system_stats():
    return {
        "load": psutil.getloadavg(),
        "memory": psutil.virtual_memory()._asdict(),
        "disks": psutil.disk_usage('/')._asdict()
    }

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7007)