<!DOCTYPE html>
<html>
<head>
    <title>FRP 综合管理平台</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .status-card { border: 1px solid #ddd; padding: 15px; margin: 10px; }
        .running { color: green; }
        .stopped { color: red; }
    </style>
</head>
<body>
    <h1>FRP 管理控制台</h1>
    
    <div class="status-card">
        <h2>FRPS 状态</h2>
        <div id="frps-status">
            <p>Loading status...</p>
        </div>
        <button onclick="controlFrps('start')">启动</button>
        <button onclick="controlFrps('stop')">停止</button>
        <button onclick="controlFrps('restart')">重启</button>
    </div>

    <div class="status-card">
        <h2>系统资源</h2>
        <canvas id="systemChart" width="400" height="200"></canvas>
    </div>

    <script>
    function updateStatus() {
        fetch('/api/status')
            .then(r => r.json())
            .then(data => {
                const statusDiv = document.getElementById('frps-status');
                if(data.frps.running) {
                    statusDiv.innerHTML = `
                        <p class="running">✅ 运行中 (PID: ${data.frps.pid})</p>
                        <p>启动命令: ${data.frps.cmd}</p>
                    `;
                } else {
                    statusDiv.innerHTML = '<p class="stopped">❌ 服务未运行</p>';
                }
                
                // 更新图表
                updateChart(data.system);
            });
    }

    function controlFrps(action) {
        fetch(`/api/frps/${action}`)
            .then(r => r.json())
            .then(() => updateStatus());
    }

    function updateChart(system) {
        // 使用Chart.js绘制资源图表
        new Chart(document.getElementById('systemChart'), {
            type: 'bar',
            data: {
                labels: ['CPU负载', '内存使用', '磁盘使用'],
                datasets: [{
                    label: '系统资源',
                    data: [
                        system.load[0],
                        system.memory.percent,
                        system.disks.percent
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.2)',
                        'rgba(54, 162, 235, 0.2)',
                        'rgba(255, 206, 86, 0.2)'
                    ]
                }]
            }
        });
    }

    // 每5秒刷新状态
    setInterval(updateStatus, 5000);
    updateStatus();
    </script>
</body>
</html>