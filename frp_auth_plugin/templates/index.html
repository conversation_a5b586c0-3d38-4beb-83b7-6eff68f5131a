{% extends "base.html" %}

{% block title %}系统监控 - FRP 管理平台{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number" id="total-users">{{ users|length }}</div>
        <div class="stat-label">总用户数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="active-ports">{{ ports|length }}</div>
        <div class="stat-label">活跃端口</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="total-logins">-</div>
        <div class="stat-label">总登录次数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number" id="total-proxies">-</div>
        <div class="stat-label">总代理数</div>
    </div>
</div>

<!-- FRPS 状态控制 -->
<div class="card">
    <h2>FRPS 服务状态</h2>
    <div id="frps-status">
        <p>正在加载状态...</p>
    </div>
    <div style="margin-top: 1rem;">
        <button class="btn btn-success" onclick="controlFrps('start')">启动服务</button>
        <button class="btn btn-danger" onclick="controlFrps('stop')">停止服务</button>
        <button class="btn btn-warning" onclick="controlFrps('restart')">重启服务</button>
        <button class="btn" onclick="updateStatus()">刷新状态</button>
    </div>
</div>

<!-- 系统资源监控 -->
<div class="card">
    <h2>系统资源监控</h2>
    <canvas id="systemChart" width="400" height="200"></canvas>
</div>

<!-- 最近活动 -->
<div class="card">
    <h2>最近端口分配</h2>
    <table class="table">
        <thead>
            <tr>
                <th>用户</th>
                <th>端口</th>
                <th>类型</th>
                <th>代理名称</th>
                <th>分配时间</th>
            </tr>
        </thead>
        <tbody>
            {% for port in ports[-10:] %}
            <tr>
                <td>{{ port.user }}</td>
                <td>{{ port.port }}</td>
                <td>{{ port.type }}</td>
                <td>{{ port.proxy_name or '-' }}</td>
                <td>
                    {% if port.timestamp %}
                        {{ port.timestamp | int | timestamp_to_date }}
                    {% else %}
                        -
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}

{% block scripts %}
<script>
let systemChart = null;

function updateStatus() {
    fetch('/api/status')
        .then(r => r.json())
        .then(data => {
            const statusDiv = document.getElementById('frps-status');
            if(data.frps.running) {
                statusDiv.innerHTML = `
                    <p class="status-running">✅ 服务运行中 (PID: ${data.frps.pid})</p>
                    <p><strong>启动命令:</strong> ${data.frps.cmd}</p>
                `;
            } else {
                statusDiv.innerHTML = '<p class="status-stopped">❌ 服务未运行</p>';
            }

            // 更新统计信息
            if (data.statistics) {
                document.getElementById('total-logins').textContent = data.statistics.total_logins || 0;
                document.getElementById('total-proxies').textContent = data.statistics.total_proxies || 0;
            }

            // 更新图表
            updateChart(data.system);
        })
        .catch(err => {
            console.error('Failed to fetch status:', err);
            document.getElementById('frps-status').innerHTML =
                '<p class="status-stopped">❌ 无法获取状态</p>';
        });
}

function controlFrps(action) {
    const button = event.target;
    const originalText = button.textContent;
    button.disabled = true;
    button.textContent = '执行中...';

    fetch(`/api/frps/${action}`)
        .then(r => r.json())
        .then(data => {
            if (data.status === 'success') {
                setTimeout(updateStatus, 1000); // 延迟更新状态
            } else {
                alert(`操作失败: ${data.message || data.error}`);
            }
        })
        .catch(err => {
            console.error('Control error:', err);
            alert('操作失败，请检查网络连接');
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = originalText;
        });
}

function updateChart(system) {
    const ctx = document.getElementById('systemChart').getContext('2d');

    if (systemChart) {
        systemChart.destroy();
    }

    systemChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['CPU负载', '内存使用率', '磁盘使用率'],
            datasets: [{
                data: [
                    (system.load[0] * 100).toFixed(1),
                    system.memory.percent.toFixed(1),
                    system.disks ? system.disks.percent.toFixed(1) : 0
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            }
        }
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStatus();
    // 每10秒刷新一次状态
    setInterval(updateStatus, 10000);
});
</script>
{% endblock %}