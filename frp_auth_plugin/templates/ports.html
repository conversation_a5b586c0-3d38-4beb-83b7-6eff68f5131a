{% extends "base.html" %}

{% block title %}端口管理 - FRP 管理平台{% endblock %}

{% block content %}
<div class="card">
    <h2>端口分配管理</h2>

    <div style="margin-bottom: 1rem;">
        <span style="color: #666;">
            当前分配端口数: {{ ports|length }}
        </span>
    </div>

    {% if ports %}
    <table class="table">
        <thead>
            <tr>
                <th>用户</th>
                <th>端口</th>
                <th>类型</th>
                <th>代理名称</th>
                <th>状态</th>
                <th>分配时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for port in ports %}
            <tr>
                <td><strong>{{ port.user }}</strong></td>
                <td><code>{{ port.port }}</code></td>
                <td>
                    <span style="background: #e2e8f0; padding: 2px 6px; border-radius: 3px; font-size: 0.9em;">
                        {{ port.type.upper() }}
                    </span>
                </td>
                <td>{{ port.proxy_name or '-' }}</td>
                <td>
                    {% if port.status == 'active' %}
                        <span class="status-active">✅ 活跃</span>
                    {% else %}
                        <span class="status-inactive">❌ 非活跃</span>
                    {% endif %}
                </td>
                <td>
                    {% if port.timestamp %}
                        {{ port.timestamp | int | timestamp_to_date }}
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td>
                    <a href="/port/deallocate/{{ port.port }}"
                       class="btn btn-danger"
                       onclick="return confirm('确定要释放端口 {{ port.port }} 吗？')">释放端口</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div style="text-align: center; padding: 2rem; color: #666;">
        <p>暂无端口分配</p>
        <p>用户通过FRP客户端连接时会自动分配端口</p>
    </div>
    {% endif %}
</div>

<div class="card">
    <h2>端口使用统计</h2>
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ ports|selectattr('type', 'equalto', 'tcp')|list|length }}</div>
            <div class="stat-label">TCP 端口</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ ports|selectattr('type', 'equalto', 'udp')|list|length }}</div>
            <div class="stat-label">UDP 端口</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ ports|selectattr('type', 'equalto', 'http')|list|length }}</div>
            <div class="stat-label">HTTP 端口</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ ports|selectattr('type', 'equalto', 'https')|list|length }}</div>
            <div class="stat-label">HTTPS 端口</div>
        </div>
    </div>
</div>

<div class="card">
    <h2>端口管理说明</h2>
    <ul>
        <li><strong>自动分配:</strong> 用户通过FRP客户端创建代理时自动分配端口</li>
        <li><strong>权限检查:</strong> 系统会检查用户是否有权限使用指定端口</li>
        <li><strong>释放端口:</strong> 手动释放端口后，该端口可被其他用户使用</li>
        <li><strong>状态监控:</strong> 显示端口的当前使用状态</li>
    </ul>
</div>
{% endblock %}