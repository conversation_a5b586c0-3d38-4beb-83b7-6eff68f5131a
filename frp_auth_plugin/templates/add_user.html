{% extends "base.html" %}

{% block title %}添加用户 - FRP 管理平台{% endblock %}

{% block content %}
<div class="card">
    <h2>添加新用户</h2>
    
    <form method="POST">
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" name="username" required 
                   placeholder="请输入用户名" maxlength="50">
            <small>用户名用于登录认证，建议使用字母、数字和下划线</small>
        </div>
        
        <div class="form-group">
            <label for="token">认证Token</label>
            <input type="text" id="token" name="token" required 
                   placeholder="请输入认证token" minlength="8">
            <small>Token用于客户端连接认证，建议使用强密码</small>
        </div>
        
        <div class="form-group">
            <label for="allow_ports">允许端口范围</label>
            <input type="text" id="allow_ports" name="allow_ports" required 
                   placeholder="例如: 10000-20000,30000-40000,50001">
            <small>支持端口范围和单个端口，用逗号分隔。例如: 10000-20000,30000-40000,50001</small>
        </div>
        
        <div class="form-group">
            <label for="max_proxies">最大代理数</label>
            <input type="number" id="max_proxies" name="max_proxies" value="10" 
                   min="1" max="100">
            <small>用户可以同时创建的最大代理数量</small>
        </div>
        
        <div style="margin-top: 2rem;">
            <button type="submit" class="btn btn-success">添加用户</button>
            <a href="/users" class="btn">取消</a>
        </div>
    </form>
</div>

<div class="card">
    <h2>端口范围格式说明</h2>
    <ul>
        <li><strong>单个端口:</strong> 8080</li>
        <li><strong>端口范围:</strong> 10000-20000</li>
        <li><strong>多个范围:</strong> 10000-20000,30000-40000,50001</li>
        <li><strong>注意事项:</strong> 端口范围为 1-65535，避免使用系统保留端口</li>
    </ul>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('allow_ports').addEventListener('input', function(e) {
    const value = e.target.value;
    const isValid = validatePortRange(value);
    
    if (value && !isValid) {
        e.target.style.borderColor = '#f56565';
    } else {
        e.target.style.borderColor = '#ddd';
    }
});

function validatePortRange(portRange) {
    if (!portRange) return false;
    
    const ranges = portRange.split(',');
    for (let range of ranges) {
        range = range.trim();
        if (range.includes('-')) {
            const [start, end] = range.split('-').map(p => parseInt(p.trim()));
            if (isNaN(start) || isNaN(end) || start < 1 || end > 65535 || start > end) {
                return false;
            }
        } else {
            const port = parseInt(range);
            if (isNaN(port) || port < 1 || port > 65535) {
                return false;
            }
        }
    }
    return true;
}
</script>
{% endblock %}
