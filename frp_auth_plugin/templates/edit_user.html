{% extends "base.html" %}

{% block title %}编辑用户 - FRP 管理平台{% endblock %}

{% block content %}
<div class="card">
    <h2>编辑用户: {{ user.username }}</h2>
    
    <form method="POST">
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" id="username" value="{{ user.username }}" disabled>
            <small>用户名不可修改</small>
        </div>
        
        <div class="form-group">
            <label for="token">新认证Token</label>
            <input type="text" id="token" name="token" 
                   placeholder="留空表示不修改token">
            <small>如需修改token请输入新值，否则留空</small>
        </div>
        
        <div class="form-group">
            <label for="allow_ports">允许端口范围</label>
            <input type="text" id="allow_ports" name="allow_ports" 
                   value="{{ user.allow_ports }}" required>
            <small>支持端口范围和单个端口，用逗号分隔</small>
        </div>
        
        <div class="form-group">
            <label for="max_proxies">最大代理数</label>
            <input type="number" id="max_proxies" name="max_proxies" 
                   value="{{ user.max_proxies or 10 }}" min="1" max="100">
            <small>用户可以同时创建的最大代理数量</small>
        </div>
        
        <div class="form-group">
            <label for="status">用户状态</label>
            <select id="status" name="status">
                <option value="active" {% if user.status == 'active' %}selected{% endif %}>激活</option>
                <option value="inactive" {% if user.status == 'inactive' %}selected{% endif %}>禁用</option>
            </select>
            <small>禁用的用户无法登录和创建代理</small>
        </div>
        
        <div style="margin-top: 2rem;">
            <button type="submit" class="btn btn-success">保存修改</button>
            <a href="/users" class="btn">取消</a>
        </div>
    </form>
</div>

<div class="card">
    <h2>用户信息</h2>
    <table class="table">
        <tr>
            <td><strong>创建时间</strong></td>
            <td>{{ user.created_at | int | timestamp_to_date if user.created_at else '-' }}</td>
        </tr>
        <tr>
            <td><strong>最后登录</strong></td>
            <td>{{ user.last_login | int | timestamp_to_date if user.last_login else '从未登录' }}</td>
        </tr>
        <tr>
            <td><strong>登录次数</strong></td>
            <td>{{ user.login_count or 0 }}</td>
        </tr>
        <tr>
            <td><strong>当前活跃代理</strong></td>
            <td>{{ user.active_proxies or 0 }}</td>
        </tr>
    </table>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('allow_ports').addEventListener('input', function(e) {
    const value = e.target.value;
    const isValid = validatePortRange(value);
    
    if (value && !isValid) {
        e.target.style.borderColor = '#f56565';
    } else {
        e.target.style.borderColor = '#ddd';
    }
});

function validatePortRange(portRange) {
    if (!portRange) return false;
    
    const ranges = portRange.split(',');
    for (let range of ranges) {
        range = range.trim();
        if (range.includes('-')) {
            const [start, end] = range.split('-').map(p => parseInt(p.trim()));
            if (isNaN(start) || isNaN(end) || start < 1 || end > 65535 || start > end) {
                return false;
            }
        } else {
            const port = parseInt(range);
            if (isNaN(port) || port < 1 || port > 65535) {
                return false;
            }
        }
    }
    return true;
}
</script>
{% endblock %}
