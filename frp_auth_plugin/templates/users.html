<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户管理 - FRP 认证管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .actions { margin-bottom: 20px; }
        .btn { padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; }
        .btn-danger { background-color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户管理</h1>
        
        <div class="actions">
            <a href="/user/add" class="btn">添加用户</a>
            <a href="/" class="btn">返回仪表盘</a>
        </div>
        
        <table>
            <tr>
                <th>用户名</th>
                <th>允许端口范围</th>
                <th>操作</th>
            </tr>
            {% for user in users %}
            <tr>
                <td>{{ user.username }}</td>
                <td>{{ user.allow_ports }}</td>
                <td>
                    <a href="/user/edit/{{ user.username }}" class="btn">编辑</a>
                    <a href="/user/delete/{{ user.username }}" class="btn btn-danger">删除</a>
                </td>
            </tr>
            {% endfor %}
        </table>
    </div>
</body>
</html>