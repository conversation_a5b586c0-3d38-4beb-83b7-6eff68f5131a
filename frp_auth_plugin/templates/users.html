{% extends "base.html" %}

{% block title %}用户管理 - FRP 管理平台{% endblock %}

{% block content %}
<div class="card">
    <h2>用户管理</h2>

    <div style="margin-bottom: 1rem;">
        <a href="/user/add" class="btn btn-success">添加用户</a>
        <span style="margin-left: 1rem; color: #666;">
            总用户数: {{ users|length }}
        </span>
    </div>

    {% if users %}
    <table class="table">
        <thead>
            <tr>
                <th>用户名</th>
                <th>允许端口范围</th>
                <th>最大代理数</th>
                <th>状态</th>
                <th>最后登录</th>
                <th>登录次数</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td><strong>{{ user.username }}</strong></td>
                <td><code>{{ user.allow_ports }}</code></td>
                <td>{{ user.max_proxies or 10 }}</td>
                <td>
                    {% if user.status == 'active' %}
                        <span class="status-active">✅ 激活</span>
                    {% else %}
                        <span class="status-inactive">❌ 禁用</span>
                    {% endif %}
                </td>
                <td>
                    {% if user.last_login %}
                        {{ user.last_login | int | timestamp_to_date }}
                    {% else %}
                        <span style="color: #999;">从未登录</span>
                    {% endif %}
                </td>
                <td>{{ user.login_count or 0 }}</td>
                <td>
                    <a href="/user/edit/{{ user.username }}" class="btn">编辑</a>
                    <a href="/user/delete/{{ user.username }}"
                       class="btn btn-danger"
                       onclick="return confirm('确定要删除用户 {{ user.username }} 吗？这将同时删除该用户的所有端口分配。')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div style="text-align: center; padding: 2rem; color: #666;">
        <p>暂无用户</p>
        <a href="/user/add" class="btn btn-success">添加第一个用户</a>
    </div>
    {% endif %}
</div>

<div class="card">
    <h2>用户管理说明</h2>
    <ul>
        <li><strong>用户名:</strong> 用于FRP客户端连接时的身份标识</li>
        <li><strong>端口范围:</strong> 用户可以使用的端口范围，支持多个范围用逗号分隔</li>
        <li><strong>最大代理数:</strong> 用户同时可以创建的最大代理数量</li>
        <li><strong>状态:</strong> 禁用的用户无法登录和创建新代理</li>
        <li><strong>删除用户:</strong> 删除用户时会同时释放该用户占用的所有端口</li>
    </ul>
</div>
{% endblock %}