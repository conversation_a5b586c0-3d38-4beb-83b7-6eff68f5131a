FRPS started (PID: 68843)
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/frp/frp_auth_plugin/data_handler.py", line 67, in log_frps_start
    data['frps_config']['last_start'] = time.time()
    ~~~~^^^^^^^^^^^^^^^
KeyError: 'frps_config'
FRPS started (PID: 68864)
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/frp/frp_auth_plugin/data_handler.py", line 67, in log_frps_start
    data['frps_config']['last_start'] = time.time()
    ~~~~^^^^^^^^^^^^^^^
KeyError: 'frps_config'
FRPS started (PID: 68878)
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "/frp/frp_auth_plugin/data_handler.py", line 67, in log_frps_start
    data['frps_config']['last_start'] = time.time()
    ~~~~^^^^^^^^^^^^^^^
KeyError: 'frps_config'
