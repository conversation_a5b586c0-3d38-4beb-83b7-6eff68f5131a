#!/usr/bin/env python3
"""
日志管理模块
统一管理所有日志文件，支持自动清理和轮转
"""

import os
import logging
import logging.handlers
from datetime import datetime
import glob
import time

class LogManager:
    def __init__(self, log_dir='log'):
        self.log_dir = os.path.join(os.path.dirname(__file__), log_dir)
        self.ensure_log_dir()
        
    def ensure_log_dir(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def get_logger(self, name, log_file=None, level=logging.INFO):
        """获取配置好的logger"""
        logger = logging.getLogger(name)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
            
        logger.setLevel(level)
        
        # 创建formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件handler（如果指定了日志文件）
        if log_file:
            log_path = os.path.join(self.log_dir, log_file)
            file_handler = logging.FileHandler(log_path, mode='w')  # 每次启动都重新创建
            file_handler.setLevel(level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def cleanup_logs(self):
        """清理所有日志文件"""
        try:
            log_files = glob.glob(os.path.join(self.log_dir, '*.log'))
            for log_file in log_files:
                try:
                    os.remove(log_file)
                    print(f"Removed log file: {log_file}")
                except Exception as e:
                    print(f"Failed to remove {log_file}: {e}")
        except Exception as e:
            print(f"Failed to cleanup logs: {e}")
    
    def get_log_files(self):
        """获取所有日志文件列表"""
        try:
            return glob.glob(os.path.join(self.log_dir, '*.log'))
        except Exception:
            return []
    
    def get_log_content(self, log_file, lines=100):
        """获取日志文件内容"""
        log_path = os.path.join(self.log_dir, log_file)
        if not os.path.exists(log_path):
            return ""
        
        try:
            with open(log_path, 'r', encoding='utf-8') as f:
                content_lines = f.readlines()
                return ''.join(content_lines[-lines:])
        except Exception as e:
            return f"Error reading log file: {e}"

# 全局日志管理器实例
log_manager = LogManager()

# 便捷函数
def get_logger(name, log_file=None, level=logging.INFO):
    """获取logger的便捷函数"""
    return log_manager.get_logger(name, log_file, level)

def cleanup_logs():
    """清理日志的便捷函数"""
    log_manager.cleanup_logs()

if __name__ == '__main__':
    # 测试日志管理器
    logger = get_logger('test', 'test.log')
    logger.info("This is a test log message")
    
    print("Log files:", log_manager.get_log_files())
    print("Log content:", log_manager.get_log_content('test.log'))
    
    cleanup_logs()
