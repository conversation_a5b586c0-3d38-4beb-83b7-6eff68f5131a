import json
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
from urllib.parse import urlparse
from data_handler import DataHandler
from log_manager import get_logger

# 尝试导入psutil，如果失败则使用模拟版本
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    # 创建模拟的psutil模块
    class MockPsutil:
        @staticmethod
        def getloadavg():
            return [0.0, 0.0, 0.0]

        @staticmethod
        def virtual_memory():
            class MockMemory:
                def _asdict(self):
                    return {"percent": 0.0, "total": 0, "available": 0}
            return MockMemory()

        @staticmethod
        def process_iter(attrs=None):
            return []

    psutil = MockPsutil()

logger = get_logger('AuthAPI', 'auth_api.log')

class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
    daemon_threads = True

class AuthHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.data_handler = DataHandler()
        super().__init__(*args, **kwargs)

    def _parse_request(self):
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length:
            try:
                return json.loads(self.rfile.read(content_length).decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return None
        return None

    def _send_response(self, status_code, body):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(body).encode('utf-8'))

    def do_GET(self):
        if self.path == '/api/status':
            self._send_response(200, self._get_system_status())
        else:
            self._send_response(404, {"error": "Not Found"})

    def do_POST(self):
        data = self._parse_request()
        if not data:
            self._send_response(400, {"error": "Invalid request"})
            return

        op = data.get('op', '')
        content = data.get('content', {})
        
        if op == 'Login':
            response = self._handle_login(content.get('user', {}))
        elif op == 'NewProxy':
            response = self._handle_new_proxy(content.get('user', {}), content.get('proxy', {}))
        else:
            response = {"reject": True, "reject_reason": "Unsupported operation"}
        
        self._send_response(200, response)

    def _get_system_status(self):
        return {
            "frps": self._check_frps(),
            "load": psutil.getloadavg(),
            "memory": psutil.virtual_memory()._asdict()
        }

    def _handle_login(self, user_info):
        """处理用户登录认证"""
        try:
            username = user_info.get('user', '')
            token = user_info.get('metas', {}).get('token', '')

            logger.info(f"Login attempt for user: {username}")

            if not username or not token:
                logger.warning(f"Login failed: missing credentials for {username}")
                return {"reject": True, "reject_reason": "Missing username or token"}

            # 验证用户凭据
            if not self.data_handler.verify_user(username, token):
                logger.warning(f"Login failed: invalid credentials for {username}")
                return {"reject": True, "reject_reason": "Invalid username or token"}

            # 记录登录成功
            self.data_handler.log_user_login(username)
            logger.info(f"Login successful for user: {username}")

            return {"reject": False, "unchange": True}

        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return {"reject": True, "reject_reason": "Internal server error"}

    def _handle_new_proxy(self, user_info, proxy_info):
        """处理新代理请求"""
        try:
            username = user_info.get('user', '')
            token = user_info.get('metas', {}).get('token', '')
            proxy_name = proxy_info.get('proxy_name', '')
            proxy_type = proxy_info.get('proxy_type', 'tcp')
            remote_port = proxy_info.get('remote_port', 0)

            logger.info(f"New proxy request from user: {username}, proxy: {proxy_name}, port: {remote_port}")

            # 验证用户凭据
            if not self.data_handler.verify_user(username, token):
                logger.warning(f"Proxy rejected: invalid credentials for {username}")
                return {"reject": True, "reject_reason": "Invalid username or token"}

            # 检查端口权限
            if not self.data_handler.check_port_permission(username, remote_port):
                logger.warning(f"Proxy rejected: port {remote_port} not allowed for user {username}")
                return {"reject": True, "reject_reason": f"Port {remote_port} not in allowed range"}

            # 检查端口是否已被占用
            if self.data_handler.is_port_allocated(remote_port):
                logger.warning(f"Proxy rejected: port {remote_port} already allocated")
                return {"reject": True, "reject_reason": f"Port {remote_port} already in use"}

            # 分配端口
            if self.data_handler.allocate_port(username, remote_port, proxy_type, proxy_name):
                logger.info(f"Proxy approved: {proxy_name} for user {username} on port {remote_port}")
                return {"reject": False, "unchange": True}
            else:
                logger.error(f"Failed to allocate port {remote_port} for user {username}")
                return {"reject": True, "reject_reason": "Failed to allocate port"}

        except Exception as e:
            logger.error(f"New proxy error: {str(e)}")
            return {"reject": True, "reject_reason": "Internal server error"}

    def _check_frps(self):
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if 'frps' in ' '.join(proc.info['cmdline'] or []):
                return {
                    "running": True,
                    "pid": proc.info['pid'],
                    "cmd": ' '.join(proc.info['cmdline'] or [])
                }
        return {"running": False}

def run_server(port=7201):
    server_address = ('', port)
    httpd = ThreadedHTTPServer(server_address, AuthHandler)
    logger.info(f"Starting FRP Auth API on port {port}...")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
    finally:
        httpd.server_close()

if __name__ == '__main__':
    run_server()