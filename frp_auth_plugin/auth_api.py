import json
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
from urllib.parse import urlparse
from data_handler import DataHandler
import psutil

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('frp_auth_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('AuthAPI')

class ThreadedHTTPServer(ThreadingMixIn, HTTPServer):
    daemon_threads = True

class AuthHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.data_handler = DataHandler()
        super().__init__(*args, **kwargs)

    def _parse_request(self):
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length:
            try:
                return json.loads(self.rfile.read(content_length).decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                return None
        return None

    def _send_response(self, status_code, body):
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(body).encode('utf-8'))

    def do_GET(self):
        if self.path == '/api/status':
            self._send_response(200, self._get_system_status())
        else:
            self._send_response(404, {"error": "Not Found"})

    def do_POST(self):
        data = self._parse_request()
        if not data:
            self._send_response(400, {"error": "Invalid request"})
            return

        op = data.get('op', '')
        content = data.get('content', {})
        
        if op == 'Login':
            response = self._handle_login(content.get('user', {}))
        elif op == 'NewProxy':
            response = self._handle_new_proxy(content.get('user', {}), content.get('proxy', {}))
        else:
            response = {"reject": True, "reject_reason": "Unsupported operation"}
        
        self._send_response(200, response)

    def _get_system_status(self):
        return {
            "frps": self._check_frps(),
            "load": psutil.getloadavg(),
            "memory": psutil.virtual_memory()._asdict()
        }

    def _check_frps(self):
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if 'frps' in ' '.join(proc.info['cmdline'] or []):
                return {
                    "running": True,
                    "pid": proc.info['pid'],
                    "cmd": ' '.join(proc.info['cmdline'] or [])
                }
        return {"running": False}

def run_server(port=7201):
    server_address = ('', port)
    httpd = ThreadedHTTPServer(server_address, AuthHandler)
    logger.info(f"Starting FRP Auth API on port {port}...")
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
    finally:
        httpd.server_close()

if __name__ == '__main__':
    run_server()