import json
import os
import threading
from typing import Dict, List, Optional
import time

class DataHandler:
    def __init__(self, file_path='token.json'):
        self.file_path = file_path
        self.lock = threading.Lock()
        self._init_file()

    def _init_file(self):
        with self.lock:
            if not os.path.exists(self.file_path):
                with open(self.file_path, 'w') as f:
                    json.dump({
                        "users": [],
                        "port_allocations": [],
                        "frps_config": {
                            "last_start": None,
                            "last_stop": None
                        }
                    }, f, indent=2)

    def _load(self):
        with self.lock:
            with open(self.file_path, 'r') as f:
                return json.load(f)

    def _save(self, data):
        with self.lock:
            with open(self.file_path, 'w') as f:
                json.dump(data, f, indent=2)

    # 用户管理方法
    def add_user(self, username: str, token: str, allow_ports: str):
        data = self._load()
        if any(u['username'] == username for u in data['users']):
            return False
        data['users'].append({
            "username": username,
            "token": token,
            "allow_ports": allow_ports,
            "created_at": time.time()
        })
        self._save(data)
        return True

    # 端口管理方法
    def allocate_port(self, user: str, port: int, proxy_type: str):
        data = self._load()
        if any(p['port'] == port for p in data['port_allocations']):
            return False
        data['port_allocations'].append({
            "user": user,
            "port": port,
            "type": proxy_type,
            "timestamp": time.time()
        })
        self._save(data)
        return True

    # FRPS状态记录
    def log_frps_start(self):
        data = self._load()
        data['frps_config']['last_start'] = time.time()
        self._save(data)

    # 其他必要方法...
    def get_all_users(self) -> List[Dict]:
        return self._load().get('users', [])

    def get_port_allocations(self) -> List[Dict]:
        return self._load().get('port_allocations', [])