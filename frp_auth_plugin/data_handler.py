import json
import os
import threading
from typing import Dict, List, Optional
import time
import re
import hashlib
import logging
from log_manager import get_logger

logger = get_logger('DataHandler', 'data_handler.log')

class DataHandler:
    def __init__(self, file_path='token.json'):
        self.file_path = file_path
        self.lock = threading.Lock()
        self._init_file()

    def _init_file(self):
        with self.lock:
            if not os.path.exists(self.file_path):
                with open(self.file_path, 'w') as f:
                    json.dump({
                        "users": [],
                        "port_allocations": [],
                        "user_sessions": [],
                        "frps_config": {
                            "last_start": None,
                            "last_stop": None,
                            "restart_count": 0
                        },
                        "statistics": {
                            "total_logins": 0,
                            "total_proxies": 0,
                            "active_users": 0
                        }
                    }, f, indent=2)

    def _load(self):
        with self.lock:
            with open(self.file_path, 'r') as f:
                return json.load(f)

    def _save(self, data):
        with self.lock:
            with open(self.file_path, 'w') as f:
                json.dump(data, f, indent=2)

    # 用户管理方法
    def add_user(self, username: str, token: str, allow_ports: str, max_proxies: int = 10):
        """添加新用户"""
        data = self._load()
        if any(u['username'] == username for u in data['users']):
            return False

        # 验证端口范围格式
        if not self._validate_port_range(allow_ports):
            return False

        data['users'].append({
            "username": username,
            "token": self._hash_token(token),
            "allow_ports": allow_ports,
            "max_proxies": max_proxies,
            "created_at": time.time(),
            "last_login": None,
            "login_count": 0,
            "active_proxies": 0,
            "status": "active"
        })
        self._save(data)
        logger.info(f"User {username} added successfully")
        return True

    def verify_user(self, username: str, token: str) -> bool:
        """验证用户凭据"""
        data = self._load()
        hashed_token = self._hash_token(token)

        for user in data['users']:
            if (user['username'] == username and
                user['token'] == hashed_token and
                user['status'] == 'active'):
                return True
        return False

    def update_user(self, username: str, **kwargs) -> bool:
        """更新用户信息"""
        data = self._load()
        for user in data['users']:
            if user['username'] == username:
                for key, value in kwargs.items():
                    if key == 'token':
                        user[key] = self._hash_token(value)
                    elif key in ['allow_ports', 'max_proxies', 'status']:
                        user[key] = value
                self._save(data)
                logger.info(f"User {username} updated")
                return True
        return False

    def delete_user(self, username: str) -> bool:
        """删除用户"""
        data = self._load()
        original_count = len(data['users'])
        data['users'] = [u for u in data['users'] if u['username'] != username]

        if len(data['users']) < original_count:
            # 同时删除该用户的端口分配
            data['port_allocations'] = [p for p in data['port_allocations'] if p['user'] != username]
            self._save(data)
            logger.info(f"User {username} deleted")
            return True
        return False

    def get_user(self, username: str) -> Optional[Dict]:
        """获取用户信息"""
        data = self._load()
        for user in data['users']:
            if user['username'] == username:
                # 不返回token哈希
                user_copy = user.copy()
                user_copy.pop('token', None)
                return user_copy
        return None

    # 端口管理方法
    def allocate_port(self, user: str, port: int, proxy_type: str, proxy_name: str = "") -> bool:
        """分配端口给用户"""
        data = self._load()

        # 检查端口是否已被占用
        if any(p['port'] == port for p in data['port_allocations']):
            return False

        # 检查用户是否达到最大代理数限制
        user_proxies = len([p for p in data['port_allocations'] if p['user'] == user])
        user_info = self.get_user(user)
        if user_info and user_proxies >= user_info.get('max_proxies', 10):
            logger.warning(f"User {user} reached max proxy limit")
            return False

        data['port_allocations'].append({
            "user": user,
            "port": port,
            "type": proxy_type,
            "proxy_name": proxy_name,
            "timestamp": time.time(),
            "status": "active"
        })

        # 更新统计信息
        data['statistics']['total_proxies'] += 1

        self._save(data)
        logger.info(f"Port {port} allocated to user {user}")
        return True

    def deallocate_port(self, port: int) -> bool:
        """释放端口"""
        data = self._load()
        original_count = len(data['port_allocations'])
        data['port_allocations'] = [p for p in data['port_allocations'] if p['port'] != port]

        if len(data['port_allocations']) < original_count:
            self._save(data)
            logger.info(f"Port {port} deallocated")
            return True
        return False

    def is_port_allocated(self, port: int) -> bool:
        """检查端口是否已被分配"""
        data = self._load()
        return any(p['port'] == port for p in data['port_allocations'])

    def check_port_permission(self, username: str, port: int) -> bool:
        """检查用户是否有权限使用指定端口"""
        user = self.get_user(username)
        if not user:
            return False

        allow_ports = user.get('allow_ports', '')
        return self._is_port_in_range(port, allow_ports)

    def get_user_ports(self, username: str) -> List[Dict]:
        """获取用户的端口分配"""
        data = self._load()
        return [p for p in data['port_allocations'] if p['user'] == username]

    # 会话管理
    def log_user_login(self, username: str):
        """记录用户登录"""
        data = self._load()

        # 更新用户登录信息
        for user in data['users']:
            if user['username'] == username:
                user['last_login'] = time.time()
                user['login_count'] = user.get('login_count', 0) + 1
                break

        # 记录会话
        data['user_sessions'].append({
            "username": username,
            "login_time": time.time(),
            "ip": "unknown"  # 可以从请求中获取
        })

        # 更新统计
        data['statistics']['total_logins'] += 1

        self._save(data)

    # FRPS状态记录
    def log_frps_start(self):
        data = self._load()
        data['frps_config']['last_start'] = time.time()
        data['frps_config']['restart_count'] = data['frps_config'].get('restart_count', 0) + 1
        self._save(data)

    def log_frps_stop(self):
        data = self._load()
        data['frps_config']['last_stop'] = time.time()
        self._save(data)

    # 工具方法
    def _hash_token(self, token: str) -> str:
        """对token进行哈希处理"""
        return hashlib.sha256(token.encode()).hexdigest()

    def _validate_port_range(self, port_range: str) -> bool:
        """验证端口范围格式"""
        if not port_range:
            return False

        ranges = port_range.split(',')
        for range_str in ranges:
            range_str = range_str.strip()
            if '-' in range_str:
                try:
                    start, end = map(int, range_str.split('-'))
                    if start < 1 or end > 65535 or start > end:
                        return False
                except ValueError:
                    return False
            else:
                try:
                    port = int(range_str)
                    if port < 1 or port > 65535:
                        return False
                except ValueError:
                    return False
        return True

    def _is_port_in_range(self, port: int, port_range: str) -> bool:
        """检查端口是否在允许的范围内"""
        if not port_range:
            return False

        ranges = port_range.split(',')
        for range_str in ranges:
            range_str = range_str.strip()
            if '-' in range_str:
                try:
                    start, end = map(int, range_str.split('-'))
                    if start <= port <= end:
                        return True
                except ValueError:
                    continue
            else:
                try:
                    if int(range_str) == port:
                        return True
                except ValueError:
                    continue
        return False

    # 获取方法
    def get_all_users(self) -> List[Dict]:
        """获取所有用户（不包含token）"""
        data = self._load()
        users = []
        for user in data.get('users', []):
            user_copy = user.copy()
            user_copy.pop('token', None)  # 移除敏感信息
            users.append(user_copy)
        return users

    def get_port_allocations(self) -> List[Dict]:
        return self._load().get('port_allocations', [])

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        data = self._load()
        stats = data.get('statistics', {})

        # 计算活跃用户数
        active_users = len(set(p['user'] for p in data.get('port_allocations', [])))
        stats['active_users'] = active_users

        return stats

    def get_frps_config(self) -> Dict:
        """获取FRPS配置信息"""
        return self._load().get('frps_config', {})