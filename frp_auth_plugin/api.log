Traceback (most recent call last):
  File "/frp/frp_auth_plugin/auth_api.py", line 122, in <module>
    run_server(args.port)
  File "/frp/frp_auth_plugin/auth_api.py", line 106, in run_server
    httpd = ThreadedHTTPServer(server_address, AuthHandler)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/socketserver.py", line 456, in __init__
    self.server_bind()
  File "/usr/lib/python3.11/http/server.py", line 136, in server_bind
    socketserver.TCPServer.server_bind(self)
  File "/usr/lib/python3.11/socketserver.py", line 472, in server_bind
    self.socket.bind(self.server_address)
OSError: [Errno 98] Address already in use
Traceback (most recent call last):
  File "/frp/frp_auth_plugin/auth_api.py", line 7, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
Traceback (most recent call last):
  File "/frp/frp_auth_plugin/auth_api.py", line 7, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
Traceback (most recent call last):
  File "/frp/frp_auth_plugin/auth_api.py", line 7, in <module>
    import psutil
ModuleNotFoundError: No module named 'psutil'
