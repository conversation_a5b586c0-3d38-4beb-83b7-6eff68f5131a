#!/usr/bin/env python3
"""
依赖检查模块
检查Python环境和必需的依赖包
"""

import sys
import subprocess
import importlib
import os

class DependencyChecker:
    def __init__(self):
        self.required_packages = {
            'flask': 'Flask>=2.0.0'
        }
        self.python_min_version = (3, 6)
    
    def check_python_version(self):
        """检查Python版本"""
        current_version = sys.version_info[:2]
        if current_version < self.python_min_version:
            print(f"❌ Python版本过低: {sys.version}")
            print(f"   需要Python {'.'.join(map(str, self.python_min_version))} 或更高版本")
            return False
        
        print(f"✅ Python版本: {sys.version.split()[0]}")
        return True
    
    def check_package(self, package_name):
        """检查单个包是否安装"""
        try:
            importlib.import_module(package_name)
            print(f"✅ {package_name} 已安装")
            return True
        except ImportError:
            print(f"❌ {package_name} 未安装")
            return False
    
    def check_all_packages(self):
        """检查所有必需的包"""
        missing_packages = []

        for package_name in self.required_packages:
            if not self.check_package(package_name):
                missing_packages.append(package_name)

        return missing_packages
    
    def install_package(self, package_spec):
        """安装单个包"""
        try:
            print(f"正在安装 {package_spec}...")
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', package_spec],
                capture_output=True, text=True, check=True
            )
            print(f"✅ {package_spec} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_spec} 安装失败:")
            print(f"   错误: {e.stderr}")
            return False
    
    def install_missing_packages(self, missing_packages):
        """安装缺失的包"""
        if not missing_packages:
            return True
        
        print(f"\n发现 {len(missing_packages)} 个缺失的依赖包")
        print("正在尝试自动安装...")
        
        success = True
        for package_name in missing_packages:
            package_spec = self.required_packages[package_name]
            if not self.install_package(package_spec):
                success = False
        
        return success
    
    def check_pip(self):
        """检查pip是否可用"""
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', '--version'],
                capture_output=True, text=True, check=True
            )
            print(f"✅ pip 可用: {result.stdout.strip()}")
            return True
        except subprocess.CalledProcessError:
            print("❌ pip 不可用")
            print("   请先安装pip: https://pip.pypa.io/en/stable/installation/")
            return False
    
    def check_requirements_file(self):
        """检查requirements.txt文件"""
        req_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'requirements.txt')
        if os.path.exists(req_file):
            print(f"✅ 找到requirements.txt: {req_file}")
            return req_file
        else:
            print("⚠️  未找到requirements.txt文件")
            return None
    
    def install_from_requirements(self):
        """从requirements.txt安装依赖"""
        req_file = self.check_requirements_file()
        if not req_file:
            return False
        
        try:
            print("正在从requirements.txt安装依赖...")
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'install', '-r', req_file],
                capture_output=True, text=True, check=True
            )
            print("✅ 依赖安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print("❌ 依赖安装失败:")
            print(f"   错误: {e.stderr}")
            return False
    
    def full_check(self, auto_install=True):
        """完整的依赖检查"""
        print("=" * 50)
        print("FRP管理系统 - 依赖检查")
        print("=" * 50)
        
        # 检查Python版本
        if not self.check_python_version():
            return False
        
        # 检查pip
        if not self.check_pip():
            return False
        
        # 检查包
        missing_packages = self.check_all_packages()
        
        if missing_packages:
            if auto_install:
                # 尝试从requirements.txt安装
                if self.install_from_requirements():
                    # 重新检查
                    missing_packages = self.check_all_packages()

                # 如果还有缺失，逐个安装
                if missing_packages:
                    if not self.install_missing_packages(missing_packages):
                        # 如果只是psutil安装失败，给出警告但继续运行
                        final_missing = self.check_all_packages()
                        if final_missing == ['psutil']:
                            print(f"\n⚠️  psutil安装失败，系统监控功能将受限")
                            print("   核心功能仍可正常使用")
                            print("   建议稍后手动安装: pip install psutil")
                        else:
                            print(f"\n❌ 仍有 {len(final_missing)} 个包未能安装成功")
                            return False
                    else:
                        # 最终检查
                        final_missing = self.check_all_packages()
                        if final_missing:
                            print(f"\n❌ 仍有 {len(final_missing)} 个包未能安装成功")
                            return False
            else:
                print(f"\n❌ 缺少 {len(missing_packages)} 个必需的依赖包")
                print("请运行以下命令安装:")
                print(f"pip install {' '.join(self.required_packages.values())}")
                return False
        
        print("\n✅ 所有依赖检查通过!")
        return True

def main():
    """主函数"""
    checker = DependencyChecker()
    
    # 检查命令行参数
    auto_install = '--no-install' not in sys.argv
    
    if checker.full_check(auto_install):
        print("\n🎉 系统准备就绪，可以启动FRP管理系统")
        sys.exit(0)
    else:
        print("\n💥 依赖检查失败，请解决上述问题后重试")
        sys.exit(1)

if __name__ == '__main__':
    main()
