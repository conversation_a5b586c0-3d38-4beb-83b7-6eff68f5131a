# FRP 综合管理系统

一个功能完整的FRP服务器管理系统，支持多用户认证、端口权限控制、Web管理界面和系统监控。

## 🚀 功能特性

- ✅ **多用户认证管理** - 支持用户注册、登录验证
- ✅ **端口权限控制** - 灵活的端口范围分配和权限管理
- ✅ **Web管理界面** - 直观的可视化管理控制台
- ✅ **系统监控** - 实时监控FRPS服务状态和系统资源
- ✅ **日志管理** - 统一的日志记录和自动清理
- ✅ **依赖检查** - 自动检查和安装Python依赖

## 📁 目录结构

```
frps程序/
├── frps                    # FRP服务器可执行文件
├── frps.toml              # FRP服务器配置文件
├── requirements.txt       # Python依赖包列表
├── start_frp_system.sh   # 系统启动脚本
├── stop_frp_system.sh    # 系统停止脚本
├── frp_auth_plugin/       # 认证插件目录
│   ├── auth_api.py        # 认证API服务
│   ├── web_ui.py          # Web管理界面
│   ├── data_handler.py    # 数据处理模块
│   ├── log_manager.py     # 日志管理模块
│   ├── dependency_checker.py # 依赖检查模块
│   ├── frp_control.sh     # FRPS控制脚本
│   ├── run.sh             # 插件启动脚本
│   ├── token.json         # 用户数据文件
│   ├── log/               # 统一日志目录
│   └── templates/         # Web界面模板
└── log/                   # 主日志目录
```

## 🛠️ 安装和使用

### 1. 系统要求

- Python 3.6 或更高版本
- pip 包管理器
- Linux/macOS 系统

### 2. 快速启动

```bash
# 启动整个系统（自动检查依赖）
./start_frp_system.sh

# 停止整个系统（自动清理日志）
./stop_frp_system.sh
```

### 3. 手动安装依赖

如果自动安装失败，可以手动安装：

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者单独安装
pip install flask>=2.0.0 psutil>=5.8.0
```

### 4. 访问管理界面

启动成功后，可以通过以下地址访问：

- **Web管理界面**: http://localhost:7007
- **认证API**: http://localhost:7201
- **FRPS服务端口**: 7000

## 📋 使用说明

### 用户管理

1. 访问 Web 管理界面
2. 点击"用户管理"
3. 添加新用户，设置用户名、token和允许的端口范围
4. 管理用户状态（激活/禁用）

### 端口管理

- 用户创建代理时自动分配端口
- 系统会检查用户权限和端口可用性
- 可以手动释放不再使用的端口

### 系统监控

- 实时显示FRPS服务状态
- 监控系统资源使用情况
- 查看用户活动统计

## 🔧 配置说明

### FRPS配置 (frps.toml)

```toml
[common]
bind_port = 7000

[plugin.auth]
addr = "127.0.0.1:7201"
path = "/"
ops = ["Login", "NewProxy"]
```

### 用户数据格式 (token.json)

```json
{
  "users": [
    {
      "username": "admin",
      "token": "hashed_token",
      "allow_ports": "10000-20000,30000-40000",
      "max_proxies": 10,
      "status": "active"
    }
  ],
  "port_allocations": [
    {
      "user": "admin",
      "port": 10001,
      "type": "tcp",
      "proxy_name": "ssh",
      "timestamp": 1234567890
    }
  ]
}
```

## 📝 日志管理

- 所有日志统一存放在 `frp_auth_plugin/log/` 目录
- 每次启动时自动清理旧日志
- 停止服务时自动删除日志文件
- 支持实时查看日志：`tail -f frp_auth_plugin/log/*.log`

## 🔒 安全特性

- Token采用SHA256哈希存储
- 端口权限严格验证
- 用户状态控制
- 请求日志记录

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   # 手动安装依赖
   pip install flask psutil
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :7000
   netstat -tlnp | grep :7201
   netstat -tlnp | grep :7007
   ```

3. **FRPS启动失败**
   ```bash
   # 检查FRPS配置
   ./frps -c frps.toml
   ```

4. **查看详细日志**
   ```bash
   # 查看所有日志
   tail -f frp_auth_plugin/log/*.log
   ```

## 📞 技术支持

如遇问题，请检查：
1. Python版本是否符合要求
2. 依赖包是否正确安装
3. 端口是否被占用
4. 日志文件中的错误信息

---

**注意**: 首次使用请确保系统已安装Python3和pip，启动脚本会自动检查和安装所需依赖。
