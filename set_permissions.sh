#!/bin/bash

# FRP系统权限设置脚本
# 为所有需要执行权限的文件设置权限

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}FRP系统 - 设置执行权限${NC}"
echo "=================================="

# 需要设置执行权限的文件列表
FILES=(
    "frps"
    "start_frp_system.sh"
    "stop_frp_system.sh"
    "set_permissions.sh"
    "frp_auth_plugin/dependency_checker.py"
)

# 设置权限
for file in "${FILES[@]}"; do
    file_path="$SCRIPT_DIR/$file"
    if [ -f "$file_path" ]; then
        chmod +x "$file_path"
        if [ -x "$file_path" ]; then
            echo -e "✅ $file"
        else
            echo -e "${YELLOW}⚠️  $file (权限设置可能失败)${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  $file (文件不存在)${NC}"
    fi
done

echo ""
echo -e "${GREEN}权限设置完成！${NC}"
echo ""
echo "现在可以运行："
echo "  ./start_frp_system.sh  # 启动系统"
echo "  ./stop_frp_system.sh   # 停止系统"
