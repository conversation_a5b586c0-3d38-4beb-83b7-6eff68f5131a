#!/bin/bash

# FRP系统主启动脚本
# 启动顺序: 依赖检查 -> FRPS服务 -> 管理插件

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PLUGIN_DIR="$SCRIPT_DIR/frp_auth_plugin"
FRPS_BIN="$SCRIPT_DIR/frps"
FRPS_CONFIG="$SCRIPT_DIR/frps.toml"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================================="
    echo "                    FRP 综合管理系统"
    echo "=================================================================="
    echo -e "${NC}"
    echo "功能特性:"
    echo "  ✅ 多用户认证管理"
    echo "  ✅ 端口权限控制"
    echo "  ✅ Web管理界面"
    echo "  ✅ 系统监控"
    echo "  ✅ 日志管理"
    echo ""
}

# 检查文件是否存在
check_files() {
    log "检查必要文件..."
    
    if [ ! -f "$FRPS_BIN" ]; then
        error "FRPS可执行文件不存在: $FRPS_BIN"
        return 1
    fi
    
    if [ ! -f "$FRPS_CONFIG" ]; then
        error "FRPS配置文件不存在: $FRPS_CONFIG"
        return 1
    fi
    
    if [ ! -d "$PLUGIN_DIR" ]; then
        error "认证插件目录不存在: $PLUGIN_DIR"
        return 1
    fi
    
    log "文件检查通过"
    return 0
}

# 检查Python依赖
check_dependencies() {
    log "检查Python环境和依赖..."
    
    # 检查Python3是否存在
    if ! command -v python3 &> /dev/null; then
        error "Python3 未安装，请先安装Python3"
        return 1
    fi
    
    # 运行依赖检查脚本
    cd "$PLUGIN_DIR"
    if ! python3 dependency_checker.py; then
        error "依赖检查失败"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
    return 0
}

# 清理旧日志
cleanup_logs() {
    log "清理旧日志文件..."
    cd "$PLUGIN_DIR"
    python3 -c "from log_manager import cleanup_logs; cleanup_logs()" 2>/dev/null || true
    cd "$SCRIPT_DIR"
}

# 检查FRPS兼容性
check_frps_compatibility() {
    # 检查FRPS是否可执行
    if [ -x "$FRPS_BIN" ]; then
        # 检查系统架构和文件类型
        local system_arch=$(uname -m)
        local file_info=$(file "$FRPS_BIN" 2>/dev/null)

        # 如果是ARM64系统但FRPS是x86-64，则不兼容
        if [[ "$system_arch" == "arm64" && "$file_info" == *"x86-64"* ]]; then
            return 1
        fi

        # 尝试简单的版本检查（超时保护）
        if timeout 3 "$FRPS_BIN" --help >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# 启动FRPS服务
start_frps() {
    log "启动FRPS服务..."

    # 检查是否已经运行
    if pgrep -f "frps.*frps.toml\|frps_mock.py" > /dev/null; then
        warn "FRPS服务已在运行"
        return 0
    fi

    local frps_cmd=""
    local frps_args=""

    # 检查FRPS兼容性
    if check_frps_compatibility; then
        log "使用原生FRPS服务器"
        frps_cmd="$FRPS_BIN"
        frps_args="-c $FRPS_CONFIG"
    else
        warn "原生FRPS不兼容当前系统，使用模拟FRPS"
        frps_cmd="python3"
        frps_args="$SCRIPT_DIR/frps_mock.py -c $FRPS_CONFIG"
    fi

    # 启动FRPS
    nohup $frps_cmd $frps_args > "$PLUGIN_DIR/log/frps.log" 2>&1 &
    local frps_pid=$!

    # 等待启动
    sleep 3

    # 检查是否启动成功
    if ps -p $frps_pid > /dev/null 2>&1; then
        log "FRPS服务启动成功 (PID: $frps_pid)"
        echo $frps_pid > "$PLUGIN_DIR/frps.pid"
        return 0
    else
        error "FRPS服务启动失败"
        return 1
    fi
}

# 启动认证API
start_auth_api() {
    log "启动认证API服务..."
    
    cd "$PLUGIN_DIR"
    
    # 检查是否已经运行
    if pgrep -f "auth_api.py" > /dev/null; then
        warn "认证API已在运行"
        cd "$SCRIPT_DIR"
        return 0
    fi
    
    # 启动认证API
    nohup python3 auth_api.py > log/auth_api.log 2>&1 &
    local api_pid=$!
    
    # 等待启动
    sleep 2
    
    # 检查是否启动成功
    if ps -p $api_pid > /dev/null 2>&1; then
        log "认证API启动成功 (PID: $api_pid)"
        echo $api_pid > "auth_api.pid"
        cd "$SCRIPT_DIR"
        return 0
    else
        error "认证API启动失败"
        cd "$SCRIPT_DIR"
        return 1
    fi
}

# 启动Web界面
start_web_ui() {
    log "启动Web管理界面..."
    
    cd "$PLUGIN_DIR"
    
    # 检查是否已经运行
    if pgrep -f "web_ui.py" > /dev/null; then
        warn "Web界面已在运行"
        cd "$SCRIPT_DIR"
        return 0
    fi
    
    # 启动Web界面
    nohup python3 web_ui.py > log/web_ui.log 2>&1 &
    local web_pid=$!
    
    # 等待启动
    sleep 2
    
    # 检查是否启动成功
    if ps -p $web_pid > /dev/null 2>&1; then
        log "Web界面启动成功 (PID: $web_pid)"
        echo $web_pid > "web_ui.pid"
        cd "$SCRIPT_DIR"
        return 0
    else
        error "Web界面启动失败"
        cd "$SCRIPT_DIR"
        return 1
    fi
}

# 显示服务状态
show_status() {
    echo ""
    log "服务状态检查:"
    
    # FRPS状态
    if pgrep -f "frps.*frps.toml" > /dev/null; then
        echo -e "  FRPS服务:     ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  FRPS服务:     ${RED}❌ 未运行${NC}"
    fi
    
    # 认证API状态
    if pgrep -f "auth_api.py" > /dev/null; then
        echo -e "  认证API:      ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  认证API:      ${RED}❌ 未运行${NC}"
    fi
    
    # Web界面状态
    if pgrep -f "web_ui.py" > /dev/null; then
        echo -e "  Web管理界面:  ${GREEN}✅ 运行中${NC}"
    else
        echo -e "  Web管理界面:  ${RED}❌ 未运行${NC}"
    fi
    
    echo ""
    log "访问地址:"
    echo "  Web管理界面: http://localhost:7007"
    echo "  认证API:     http://localhost:7201"
    echo "  FRPS端口:    7000"
    echo ""
}

# 主启动流程
main() {
    show_banner
    
    # 1. 检查文件
    if ! check_files; then
        exit 1
    fi
    
    # 2. 清理旧日志
    cleanup_logs
    
    # 3. 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 4. 启动FRPS
    if ! start_frps; then
        exit 1
    fi
    
    # 5. 启动认证API
    if ! start_auth_api; then
        exit 1
    fi
    
    # 6. 启动Web界面
    if ! start_web_ui; then
        exit 1
    fi
    
    # 7. 显示状态
    show_status
    
    log "FRP系统启动完成!"
    echo ""
    echo "💡 提示:"
    echo "  - 使用 './stop_frp_system.sh' 停止所有服务"
    echo "  - 查看日志: tail -f frp_auth_plugin/log/*.log"
    echo "  - 管理界面: http://localhost:7007"
}

# 执行主函数
main "$@"
